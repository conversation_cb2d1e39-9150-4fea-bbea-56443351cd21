import { Test, TestingModule } from '@nestjs/testing';
import { Job } from 'bullmq';
import { PushProcessor } from './push.processor';
import { NotificationService } from '../../notification/notification.service';
import { PushJobType } from '../queue.constants';
import type { SinglePushJobData, BulkPushJobData } from '../queue.types';

describe('PushProcessor', () => {
  let processor: PushProcessor;
  let mockNotificationService: jest.Mocked<NotificationService>;

  beforeEach(async () => {
    // Create mock notification service
    mockNotificationService = {
      sendNotification: jest.fn(),
      sendTopicNotification: jest.fn(),
      sendNotificationToMultipleTokens: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PushProcessor,
        {
          provide: NotificationService,
          useValue: mockNotificationService,
        },
      ],
    }).compile();

    processor = module.get<PushProcessor>(PushProcessor);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('process', () => {
    it('should be defined', () => {
      expect(processor).toBeDefined();
    });

    it('should process single push notification job successfully', async () => {
      const job = {
        id: 'test-job-1',
        name: PushJobType.SEND_SINGLE,
        data: {
          token: 'fcm-token-1234567890abcdef',
          title: 'Test Push Notification',
          body: 'This is a test push notification',
          data: { key: 'value', type: 'test' },
        },
      } as unknown as Job<SinglePushJobData>;

      const mockResult = {
        status: true,
        messageId: 'message-123',
      };

      mockNotificationService.sendNotification.mockResolvedValue(mockResult);

      const result = await processor.process(job);

      expect(mockNotificationService.sendNotification).toHaveBeenCalledWith({
        token: 'fcm-token-1234567890abcdef',
        notification: {
          title: 'Test Push Notification',
          body: 'This is a test push notification',
        },
        data: { key: 'value', type: 'test' },
      });

      expect(result).toEqual({
        success: true,
        message: 'Push notification sent successfully',
        data: {
          status: true,
          messageId: 'message-123',
          summary:
            'Title: Test Push Notification, Body: This is a test push notificati...',
        },
      });
    });

    it('should process single push notification with short content', async () => {
      const job = {
        id: 'test-job-2',
        name: PushJobType.SEND_SINGLE,
        data: {
          token: 'fcm-token-short',
          title: 'Short Title',
          body: 'Short body',
          data: {},
        },
      } as unknown as Job<SinglePushJobData>;

      const mockResult = {
        status: true,
        messageId: 'message-456',
      };

      mockNotificationService.sendNotification.mockResolvedValue(mockResult);

      const result = await processor.process(job);

      expect(mockNotificationService.sendNotification).toHaveBeenCalledWith({
        token: 'fcm-token-short',
        notification: {
          title: 'Short Title',
          body: 'Short body',
        },
        data: {},
      });

      expect(result).toEqual({
        success: true,
        message: 'Push notification sent successfully',
        data: {
          status: true,
          messageId: 'message-456',
          summary: 'Title: Short Title, Body: Short body',
        },
      });
    });

    it('should process single push notification without data', async () => {
      const job = {
        id: 'test-job-3',
        name: PushJobType.SEND_SINGLE,
        data: {
          token: 'fcm-token-no-data',
          title: 'No Data Title',
          body: 'No data body',
        },
      } as unknown as Job<SinglePushJobData>;

      const mockResult = {
        status: true,
        messageId: 'message-789',
      };

      mockNotificationService.sendNotification.mockResolvedValue(mockResult);

      const result = await processor.process(job);

      expect(mockNotificationService.sendNotification).toHaveBeenCalledWith({
        token: 'fcm-token-no-data',
        notification: {
          title: 'No Data Title',
          body: 'No data body',
        },
        data: undefined,
      });

      expect(result).toEqual({
        success: true,
        message: 'Push notification sent successfully',
        data: {
          status: true,
          messageId: 'message-789',
          summary: 'Title: No Data Title, Body: No data body',
        },
      });
    });

    it('should handle single push notification failure', async () => {
      const job = {
        id: 'test-job-4',
        name: PushJobType.SEND_SINGLE,
        data: {
          token: 'invalid-token',
          title: 'Failed Notification',
          body: 'This notification will fail',
          data: { test: 'data' },
        },
      } as unknown as Job<SinglePushJobData>;

      mockNotificationService.sendNotification.mockRejectedValue(
        new Error('Invalid FCM token'),
      );

      const result = await processor.process(job);

      expect(result).toEqual({
        success: false,
        message: 'Failed to send push notification',
        error: 'Invalid FCM token',
        data: {
          token: 'invalid-to...',
          title: 'Failed Notification',
          bodyPreview: 'This notification will fail...',
        },
      });
    });

    it('should process bulk push notification job successfully', async () => {
      const job = {
        id: 'test-job-5',
        name: PushJobType.SEND_BULK,
        data: {
          tokens: ['token1', 'token2', 'token3'],
          title: 'Bulk Notification',
          body: 'This is a bulk notification',
          data: { type: 'announcement' },
        },
      } as unknown as Job<BulkPushJobData>;

      mockNotificationService.sendTopicNotification.mockResolvedValue({
        success: true,
        message: 'Topic notification sent successfully',
      });

      const result = await processor.process(job);

      expect(
        mockNotificationService.sendTopicNotification,
      ).toHaveBeenCalledWith({
        topic: 'all',
        title: 'Bulk Notification',
        body: 'This is a bulk notification',
        icon: undefined,
      });

      expect(result).toEqual({
        success: true,
        message: '3 push notifications sent successfully',
      });
    });

    it('should process large bulk notification with batching', async () => {
      const job = {
        id: 'test-job-6',
        name: PushJobType.SEND_BULK,
        data: {
          tokens: Array.from({ length: 1000 }, (_, i) => `token-${i}`),
          title: 'Large Bulk Notification',
          body: 'This is a large bulk notification',
          batchSize: 100,
        },
      } as unknown as Job<BulkPushJobData>;

      mockNotificationService.sendTopicNotification.mockResolvedValue({
        success: true,
        message: 'Topic notification sent successfully',
      });

      const result = await processor.process(job);

      // Should call sendTopicNotification 10 times (1000 tokens / 100 batch size)
      expect(
        mockNotificationService.sendTopicNotification,
      ).toHaveBeenCalledTimes(10);
      expect(result.success).toBe(true);
    });

    it('should process bulk notification with default batch size', async () => {
      const job = {
        id: 'test-job-7',
        name: PushJobType.SEND_BULK,
        data: {
          tokens: Array.from({ length: 600 }, (_, i) => `token-${i}`),
          title: 'Default Batch Notification',
          body: 'This uses default batch size',
        },
      } as unknown as Job<BulkPushJobData>;

      mockNotificationService.sendTopicNotification.mockResolvedValue({
        success: true,
        message: 'Topic notification sent successfully',
      });

      const result = await processor.process(job);

      // Should call sendTopicNotification 2 times (600 tokens / 500 default batch size)
      expect(
        mockNotificationService.sendTopicNotification,
      ).toHaveBeenCalledTimes(2);
    });

    it('should handle empty token list for bulk notification', async () => {
      const job = {
        id: 'test-job-8',
        name: PushJobType.SEND_BULK,
        data: {
          tokens: [],
          title: 'Empty Bulk Notification',
          body: 'This has no tokens',
        },
      } as unknown as Job<BulkPushJobData>;

      const result = await processor.process(job);

      expect(
        mockNotificationService.sendTopicNotification,
      ).not.toHaveBeenCalled();
      expect(result).toEqual({
        success: true,
        message: '0 push notifications sent successfully',
      });
    });

    it('should throw error for unknown job type', async () => {
      const job = {
        id: 'test-job-9',
        name: 'UNKNOWN_TYPE',
        data: {},
      } as unknown as Job;

      await expect(processor.process(job)).rejects.toThrow(
        'Unknown job type: UNKNOWN_TYPE',
      );
    });
  });
});
